#!/usr/bin/env python3
"""
Test script to verify JSON saving functionality
"""

import json
import os
from pathlib import Path
from datetime import datetime

# Import the JSON saving functions from the main script
import sys
sys.path.append('.')

def test_json_saving():
    """Test the JSON saving functionality"""

    # Import the functions we need to test using importlib
    import importlib.util
    spec = importlib.util.spec_from_file_location("deepseek_eng", "deepseek-eng.py")
    deepseek_eng = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(deepseek_eng)

    save_ai_response_as_json = deepseek_eng.save_ai_response_as_json
    ensure_json_save_directory = deepseek_eng.ensure_json_save_directory
    JSON_SAVE_DIR = deepseek_eng.JSON_SAVE_DIR
    
    print("🧪 Testing JSON saving functionality...")
    
    # Test 1: Ensure directory creation
    print("1. Testing directory creation...")
    ensure_json_save_directory()
    assert Path(JSON_SAVE_DIR).exists(), f"Directory {JSON_SAVE_DIR} was not created"
    print("   ✓ Directory created successfully")
    
    # Test 2: Save a sample AI response
    print("2. Testing JSON file saving...")
    
    sample_user_input = "Hello, can you help me with Python?"
    sample_ai_response = {
        "reasoning_content": "The user is asking for help with Python programming...",
        "final_content": "Of course! I'd be happy to help you with Python. What specific topic would you like assistance with?",
        "tool_calls_executed": [],
        "follow_up_content": "",
        "execution_time": datetime.now().isoformat(),
        "success": True,
        "error": None
    }
    
    # Save the response
    save_ai_response_as_json(sample_user_input, sample_ai_response)
    
    # Check if file was created
    json_files = list(Path(JSON_SAVE_DIR).glob("ai_response_*.json"))
    assert len(json_files) > 0, "No JSON files were created"
    print(f"   ✓ JSON file created: {json_files[-1]}")
    
    # Test 3: Verify JSON content
    print("3. Testing JSON content...")
    
    with open(json_files[-1], 'r', encoding='utf-8') as f:
        saved_data = json.load(f)
    
    # Check required fields
    required_fields = ["timestamp", "user_input", "ai_response", "session_info"]
    for field in required_fields:
        assert field in saved_data, f"Required field '{field}' missing from JSON"
    
    assert saved_data["user_input"] == sample_user_input, "User input not saved correctly"
    assert saved_data["ai_response"]["final_content"] == sample_ai_response["final_content"], "AI response content not saved correctly"
    
    print("   ✓ JSON content verified")
    
    # Test 4: Test with tool calls
    print("4. Testing with tool calls...")
    
    sample_response_with_tools = {
        "reasoning_content": "I need to read a file to help the user...",
        "final_content": "I've read the file and here's what I found:",
        "tool_calls_executed": [
            {
                "function_name": "read_file",
                "arguments": '{"file_path": "test.py"}',
                "call_id": "call_123",
                "result": "File content here...",
                "error": None
            }
        ],
        "follow_up_content": "Based on the file content, I can suggest improvements.",
        "execution_time": datetime.now().isoformat(),
        "success": True,
        "error": None
    }
    
    save_ai_response_as_json("Can you read test.py?", sample_response_with_tools)
    
    # Verify the new file
    json_files_after = list(Path(JSON_SAVE_DIR).glob("ai_response_*.json"))
    assert len(json_files_after) > len(json_files), "Second JSON file was not created"
    print(f"   ✓ JSON file with tool calls created: {json_files_after[-1]}")
    
    print("\n🎉 All tests passed! JSON saving functionality is working correctly.")
    print(f"📁 JSON files are saved in: {Path(JSON_SAVE_DIR).absolute()}")
    
    # Show the structure of a saved file
    print("\n📄 Sample JSON structure:")
    with open(json_files_after[-1], 'r', encoding='utf-8') as f:
        sample_content = json.load(f)
    
    print(json.dumps({
        "timestamp": sample_content["timestamp"],
        "user_input": sample_content["user_input"],
        "ai_response": {
            "reasoning_content": sample_content["ai_response"]["reasoning_content"][:50] + "..." if len(sample_content["ai_response"]["reasoning_content"]) > 50 else sample_content["ai_response"]["reasoning_content"],
            "final_content": sample_content["ai_response"]["final_content"][:50] + "..." if len(sample_content["ai_response"]["final_content"]) > 50 else sample_content["ai_response"]["final_content"],
            "tool_calls_executed": len(sample_content["ai_response"]["tool_calls_executed"]),
            "success": sample_content["ai_response"]["success"]
        },
        "session_info": sample_content["session_info"]
    }, indent=2))

if __name__ == "__main__":
    test_json_saving()
